"""
戰鬥模型
"""
from typing import List, Dict, Optional, Any, TYPE_CHECKING
import logging
from datetime import datetime
import uuid

from .enums import BattleStatus
from .combatant import Combatant
from .battle_log import BattleLogEntry

if TYPE_CHECKING:
    from rpg_system.config.loader import ConfigLoader

logger = logging.getLogger(__name__)


class Battle:
    """
    戰鬥類
    
    管理整個戰鬥的狀態和流程
    """
    
    def __init__(self, battle_id: Optional[str] = None, user_id: Optional[int] = None,
                 floor_number: Optional[int] = None, monster_group_id: Optional[str] = None):
        """
        初始化戰鬥
        
        Args:
            battle_id: 戰鬥ID，如果為None則自動生成
            user_id: 用戶ID
            floor_number: 樓層編號
            monster_group_id: 怪物群組ID
        """
        self.battle_id = battle_id or str(uuid.uuid4())
        self.user_id = user_id
        self.floor_number = floor_number
        self.monster_group_id = monster_group_id
        
        # 戰鬥狀態
        self.status = BattleStatus.PENDING
        self.current_turn = 0
        self.max_turns = 100  # 最大回合數限制
        
        # 參戰者
        self.player_combatants: List[Combatant] = []
        self.monster_combatants: List[Combatant] = []
        self.turn_order: List[Combatant] = []
        self.current_actor_index = 0
        
        # 戰鬥日誌
        self.battle_log: List[BattleLogEntry] = []
        
        # 時間戳
        self.created_at = datetime.now()
        self.started_at: Optional[datetime] = None
        self.ended_at: Optional[datetime] = None
        
        # 戰鬥結果
        self.winner_side: Optional[str] = None  # "player" or "monster"
        self.rewards: Dict[str, Any] = {}
    
    def add_player_combatant(self, combatant: Combatant):
        """
        添加玩家戰鬥者
        
        Args:
            combatant: 戰鬥者實例
        """
        combatant.is_player = True
        combatant.position = len(self.player_combatants)
        self.player_combatants.append(combatant)
        logger.info(f"Added player combatant: {combatant.name}")
    
    def add_monster_combatant(self, combatant: Combatant):
        """
        添加怪物戰鬥者
        
        Args:
            combatant: 戰鬥者實例
        """
        combatant.is_player = False
        combatant.position = len(self.monster_combatants)
        self.monster_combatants.append(combatant)
        logger.info(f"Added monster combatant: {combatant.name}")
    
    def get_all_combatants(self) -> List[Combatant]:
        """獲取所有戰鬥者"""
        return self.player_combatants + self.monster_combatants
    
    def get_alive_combatants(self, player_side: Optional[bool] = None) -> List[Combatant]:
        """
        獲取存活的戰鬥者
        
        Args:
            player_side: True為玩家方，False為怪物方，None為全部
            
        Returns:
            存活的戰鬥者列表
        """
        all_combatants = self.get_all_combatants()
        alive_combatants = [c for c in all_combatants if c.is_alive]
        
        if player_side is None:
            return alive_combatants
        
        return [c for c in alive_combatants if c.is_player == player_side]
    
    def get_combatant_by_id(self, combatant_id: str) -> Optional[Combatant]:
        """
        根據ID獲取戰鬥者
        
        Args:
            combatant_id: 戰鬥者ID
            
        Returns:
            戰鬥者實例，如果不存在則返回None
        """
        for combatant in self.get_all_combatants():
            if combatant.combatant_id == combatant_id:
                return combatant
        return None
    
    def calculate_turn_order(self):
        """
        計算行動順序
        
        根據速度屬性排序，速度高的先行動
        """
        all_alive = self.get_alive_combatants()
        
        # 按速度降序排列，速度相同時隨機排序
        self.turn_order = sorted(all_alive, 
                               key=lambda c: (c.get_current_stats().spd, c.combatant_id), 
                               reverse=True)
        
        self.current_actor_index = 0
        logger.info(f"Turn order calculated: {[c.name for c in self.turn_order]}")
    
    def get_current_actor(self) -> Optional[Combatant]:
        """獲取當前行動者"""
        if not self.turn_order or self.current_actor_index >= len(self.turn_order):
            return None
        return self.turn_order[self.current_actor_index]
    
    def advance_turn(self):
        """推進到下一個行動者"""
        if not self.turn_order:
            return
        
        self.current_actor_index += 1
        
        # 如果所有人都行動完畢，開始新回合
        if self.current_actor_index >= len(self.turn_order):
            self.start_new_turn()
    
    def start_new_turn(self):
        """開始新回合"""
        self.current_turn += 1
        self.current_actor_index = 0
        
        # 記錄回合開始
        self.add_log_entry(BattleLogEntry.create_turn_start(self.current_turn))
        
        # 重新計算行動順序（考慮死亡的戰鬥者）
        self.calculate_turn_order()
        
        # 處理回合開始事件
        self._process_turn_start_events()
        
        logger.info(f"Turn {self.current_turn} started")
    
    def _process_turn_start_events(self):
        """處理回合開始事件"""
        # MP恢復
        for combatant in self.get_alive_combatants():
            combatant.regenerate_mp()
            combatant.tick_cooldowns()
        
        # TODO: 處理狀態效果的回合開始觸發
        # TODO: 處理被動技能的回合開始觸發
    
    def check_battle_end(self) -> bool:
        """
        檢查戰鬥是否結束
        
        Returns:
            戰鬥是否結束
        """
        if self.status != BattleStatus.IN_PROGRESS:
            return True
        
        # 檢查回合數限制
        if self.current_turn >= self.max_turns:
            self.end_battle(BattleStatus.DRAW)
            return True
        
        # 檢查玩家方是否全滅
        alive_players = self.get_alive_combatants(player_side=True)
        if not alive_players:
            self.end_battle(BattleStatus.MONSTER_WIN)
            return True
        
        # 檢查怪物方是否全滅
        alive_monsters = self.get_alive_combatants(player_side=False)
        if not alive_monsters:
            self.end_battle(BattleStatus.PLAYER_WIN)
            return True
        
        return False
    
    def start_battle(self):
        """開始戰鬥"""
        if self.status != BattleStatus.PENDING:
            raise ValueError(f"Cannot start battle in status: {self.status}")
        
        self.status = BattleStatus.IN_PROGRESS
        self.started_at = datetime.now()
        
        # 計算初始行動順序
        self.calculate_turn_order()
        
        # 開始第一回合
        self.start_new_turn()
        
        logger.info(f"Battle {self.battle_id} started")
    
    def end_battle(self, result: BattleStatus):
        """
        結束戰鬥
        
        Args:
            result: 戰鬥結果
        """
        self.status = result
        self.ended_at = datetime.now()
        
        if result == BattleStatus.PLAYER_WIN:
            self.winner_side = "player"
        elif result == BattleStatus.MONSTER_WIN:
            self.winner_side = "monster"
        else:
            self.winner_side = None
        
        logger.info(f"Battle {self.battle_id} ended with result: {result.value}")
    
    def add_log_entry(self, entry: BattleLogEntry):
        """
        添加戰鬥日誌條目
        
        Args:
            entry: 日誌條目
        """
        self.battle_log.append(entry)
    
    def get_battle_summary(self) -> Dict[str, Any]:
        """
        獲取戰鬥摘要
        
        Returns:
            戰鬥摘要字典
        """
        return {
            "battle_id": self.battle_id,
            "user_id": self.user_id,
            "floor_number": self.floor_number,
            "monster_group_id": self.monster_group_id,
            "status": self.status.value,
            "current_turn": self.current_turn,
            "winner_side": self.winner_side,
            "player_combatants": [
                {
                    "id": c.combatant_id,
                    "name": c.name,
                    "hp": f"{c.current_hp}/{c.base_stats.hp}",
                    "mp": f"{c.current_mp}/{c.base_stats.max_mp}",
                    "alive": c.is_alive
                }
                for c in self.player_combatants
            ],
            "monster_combatants": [
                {
                    "id": c.combatant_id,
                    "name": c.name,
                    "hp": f"{c.current_hp}/{c.base_stats.hp}",
                    "mp": f"{c.current_mp}/{c.base_stats.max_mp}",
                    "alive": c.is_alive
                }
                for c in self.monster_combatants
            ],
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "ended_at": self.ended_at.isoformat() if self.ended_at else None,
            "log_entries": len(self.battle_log)
        }
    
    def __repr__(self) -> str:
        return (f"Battle(id='{self.battle_id}', status={self.status.value}, "
                f"turn={self.current_turn}, players={len(self.player_combatants)}, "
                f"monsters={len(self.monster_combatants)})")
    
    def __str__(self) -> str:
        return f"Battle {self.battle_id} - Turn {self.current_turn} ({self.status.value})"
