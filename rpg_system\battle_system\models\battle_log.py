"""
戰鬥日誌模型
"""
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime
import json


@dataclass
class BattleLogEntry:
    """
    戰鬥日誌條目
    
    記錄戰鬥中發生的各種事件
    """
    turn_number: int
    event_type: str
    actor_id: Optional[str]
    target_ids: List[str]
    skill_id: Optional[str]
    damage_dealt: int
    healing_done: int
    mp_consumed: int
    status_effects_applied: List[str]
    status_effects_removed: List[str]
    additional_data: Dict[str, Any]
    timestamp: datetime
    
    def __post_init__(self):
        """後初始化處理"""
        if self.target_ids is None:
            self.target_ids = []
        if self.status_effects_applied is None:
            self.status_effects_applied = []
        if self.status_effects_removed is None:
            self.status_effects_removed = []
        if self.additional_data is None:
            self.additional_data = {}
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    @classmethod
    def create_skill_use(cls, turn_number: int, actor_id: str, skill_id: str,
                        target_ids: List[str], mp_consumed: int = 0,
                        additional_data: Optional[Dict[str, Any]] = None) -> 'BattleLogEntry':
        """
        創建技能使用日誌
        
        Args:
            turn_number: 回合數
            actor_id: 施法者ID
            skill_id: 技能ID
            target_ids: 目標ID列表
            mp_consumed: 消耗的MP
            additional_data: 額外數據
            
        Returns:
            戰鬥日誌條目
        """
        return cls(
            turn_number=turn_number,
            event_type="SKILL_USE",
            actor_id=actor_id,
            target_ids=target_ids,
            skill_id=skill_id,
            damage_dealt=0,
            healing_done=0,
            mp_consumed=mp_consumed,
            status_effects_applied=[],
            status_effects_removed=[],
            additional_data=additional_data or {},
            timestamp=datetime.now()
        )
    
    @classmethod
    def create_damage(cls, turn_number: int, actor_id: Optional[str], 
                     target_id: str, damage: int, skill_id: Optional[str] = None,
                     additional_data: Optional[Dict[str, Any]] = None) -> 'BattleLogEntry':
        """
        創建傷害日誌
        
        Args:
            turn_number: 回合數
            actor_id: 傷害來源ID
            target_id: 目標ID
            damage: 傷害值
            skill_id: 技能ID
            additional_data: 額外數據
            
        Returns:
            戰鬥日誌條目
        """
        return cls(
            turn_number=turn_number,
            event_type="DAMAGE",
            actor_id=actor_id,
            target_ids=[target_id],
            skill_id=skill_id,
            damage_dealt=damage,
            healing_done=0,
            mp_consumed=0,
            status_effects_applied=[],
            status_effects_removed=[],
            additional_data=additional_data or {},
            timestamp=datetime.now()
        )
    
    @classmethod
    def create_healing(cls, turn_number: int, actor_id: Optional[str],
                      target_id: str, healing: int, skill_id: Optional[str] = None,
                      additional_data: Optional[Dict[str, Any]] = None) -> 'BattleLogEntry':
        """
        創建治療日誌
        
        Args:
            turn_number: 回合數
            actor_id: 治療來源ID
            target_id: 目標ID
            healing: 治療量
            skill_id: 技能ID
            additional_data: 額外數據
            
        Returns:
            戰鬥日誌條目
        """
        return cls(
            turn_number=turn_number,
            event_type="HEALING",
            actor_id=actor_id,
            target_ids=[target_id],
            skill_id=skill_id,
            damage_dealt=0,
            healing_done=healing,
            mp_consumed=0,
            status_effects_applied=[],
            status_effects_removed=[],
            additional_data=additional_data or {},
            timestamp=datetime.now()
        )
    
    @classmethod
    def create_status_effect(cls, turn_number: int, actor_id: Optional[str],
                           target_id: str, effect_id: str, applied: bool = True,
                           skill_id: Optional[str] = None,
                           additional_data: Optional[Dict[str, Any]] = None) -> 'BattleLogEntry':
        """
        創建狀態效果日誌
        
        Args:
            turn_number: 回合數
            actor_id: 效果來源ID
            target_id: 目標ID
            effect_id: 效果ID
            applied: True為施加，False為移除
            skill_id: 技能ID
            additional_data: 額外數據
            
        Returns:
            戰鬥日誌條目
        """
        return cls(
            turn_number=turn_number,
            event_type="STATUS_EFFECT",
            actor_id=actor_id,
            target_ids=[target_id],
            skill_id=skill_id,
            damage_dealt=0,
            healing_done=0,
            mp_consumed=0,
            status_effects_applied=[effect_id] if applied else [],
            status_effects_removed=[effect_id] if not applied else [],
            additional_data=additional_data or {},
            timestamp=datetime.now()
        )
    
    @classmethod
    def create_turn_start(cls, turn_number: int, 
                         additional_data: Optional[Dict[str, Any]] = None) -> 'BattleLogEntry':
        """
        創建回合開始日誌
        
        Args:
            turn_number: 回合數
            additional_data: 額外數據
            
        Returns:
            戰鬥日誌條目
        """
        return cls(
            turn_number=turn_number,
            event_type="TURN_START",
            actor_id=None,
            target_ids=[],
            skill_id=None,
            damage_dealt=0,
            healing_done=0,
            mp_consumed=0,
            status_effects_applied=[],
            status_effects_removed=[],
            additional_data=additional_data or {},
            timestamp=datetime.now()
        )
    
    @classmethod
    def create_turn_end(cls, turn_number: int,
                       additional_data: Optional[Dict[str, Any]] = None) -> 'BattleLogEntry':
        """
        創建回合結束日誌
        
        Args:
            turn_number: 回合數
            additional_data: 額外數據
            
        Returns:
            戰鬥日誌條目
        """
        return cls(
            turn_number=turn_number,
            event_type="TURN_END",
            actor_id=None,
            target_ids=[],
            skill_id=None,
            damage_dealt=0,
            healing_done=0,
            mp_consumed=0,
            status_effects_applied=[],
            status_effects_removed=[],
            additional_data=additional_data or {},
            timestamp=datetime.now()
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            "turn_number": self.turn_number,
            "event_type": self.event_type,
            "actor_id": self.actor_id,
            "target_ids": self.target_ids,
            "skill_id": self.skill_id,
            "damage_dealt": self.damage_dealt,
            "healing_done": self.healing_done,
            "mp_consumed": self.mp_consumed,
            "status_effects_applied": self.status_effects_applied,
            "status_effects_removed": self.status_effects_removed,
            "additional_data": self.additional_data,
            "timestamp": self.timestamp.isoformat()
        }
    
    def to_json(self) -> str:
        """轉換為JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    def __str__(self) -> str:
        """字符串表示"""
        if self.event_type == "SKILL_USE":
            return f"T{self.turn_number}: {self.actor_id} uses {self.skill_id}"
        elif self.event_type == "DAMAGE":
            return f"T{self.turn_number}: {self.target_ids[0]} takes {self.damage_dealt} damage"
        elif self.event_type == "HEALING":
            return f"T{self.turn_number}: {self.target_ids[0]} heals {self.healing_done} HP"
        elif self.event_type == "STATUS_EFFECT":
            effect_action = "gains" if self.status_effects_applied else "loses"
            effect_id = (self.status_effects_applied[0] if self.status_effects_applied 
                        else self.status_effects_removed[0])
            return f"T{self.turn_number}: {self.target_ids[0]} {effect_action} {effect_id}"
        else:
            return f"T{self.turn_number}: {self.event_type}"
