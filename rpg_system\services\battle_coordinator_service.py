"""
戰鬥協調服務

負責PVE戰鬥的準備、啟動、管理和結束
"""
from typing import List, Dict, Any, Optional, Tuple, TYPE_CHECKING
import logging
import random

from rpg_system.battle_system.models.battle import Battle
from rpg_system.battle_system.models.combatant import Combatant, CombatantStats
from rpg_system.battle_system.models.enums import BattleStatus, SkillType
from rpg_system.battle_system.models.skill_instance import SkillInstance

if TYPE_CHECKING:
    from rpg_system.config.loader import ConfigLoader
    from rpg_system.battle_system.services.attribute_calculator import AttributeCalculator
    from rpg_system.battle_system.handlers.effect_applier import EffectApplier
    from rpg_system.battle_system.handlers.target_selector import TargetSelector
    from rpg_system.battle_system.handlers.passive_trigger_handler import PassiveTriggerHandler
    from rpg_system.formula_engine.evaluator import FormulaEvaluator
    from rpg_system.repositories.player_collection_rpg_repository import PlayerCollectionRPGRepository
    from rpg_system.repositories.user_progress_rpg_repository import UserProgressRPGRepository

logger = logging.getLogger(__name__)


class BattleCoordinatorServiceError(Exception):
    """戰鬥協調服務異常"""
    pass


class BattleCoordinatorService:
    """
    戰鬥協調服務

    負責PVE戰鬥的準備、啟動、管理和結束
    """

    def __init__(
        self,
        config_loader: 'ConfigLoader',
        attribute_calculator: 'AttributeCalculator',
        effect_applier: 'EffectApplier',
        target_selector: 'TargetSelector',
        passive_trigger_handler: 'PassiveTriggerHandler',
        formula_evaluator: 'FormulaEvaluator',
        player_collection_repo: 'PlayerCollectionRPGRepository',
        user_progress_repo: 'UserProgressRPGRepository'
    ):
        """
        初始化戰鬥協調服務

        Args:
            config_loader: 配置加載器
            attribute_calculator: 屬性計算器
            effect_applier: 效果應用器
            target_selector: 目標選擇器
            passive_trigger_handler: 被動觸發處理器
            formula_evaluator: 公式求值器
            player_collection_repo: 玩家收藏倉庫
            user_progress_repo: 用戶進度倉庫
        """
        self.config_loader = config_loader
        self.attribute_calculator = attribute_calculator
        self.effect_applier = effect_applier
        self.target_selector = target_selector
        self.passive_trigger_handler = passive_trigger_handler
        self.formula_evaluator = formula_evaluator
        self.player_collection_repo = player_collection_repo
        self.user_progress_repo = user_progress_repo

    def prepare_pve_battle(self, user_id: int, floor_id: str) -> Tuple[Optional[Battle], Optional[str]]:
        """
        準備PVE戰鬥

        Args:
            user_id: 用戶ID
            floor_id: 樓層ID

        Returns:
            (Battle實例, 錯誤信息) 或 (None, 錯誤信息)
        """
        try:
            logger.info(f"準備PVE戰鬥: user_id={user_id}, floor_id={floor_id}")

            # 1. 獲取樓層配置
            floor_config = self.config_loader.get_floor_config(floor_id)
            if not floor_config:
                return None, f"樓層配置不存在: {floor_id}"

            # 2. 檢查玩家進度（可選）
            user_progress = self.user_progress_repo.get_user_progress(user_id)
            if not user_progress:
                return None, f"用戶進度不存在: {user_id}"

            # 檢查樓層是否解鎖
            if user_progress.current_floor_unlocked < int(floor_id):
                return None, f"樓層未解鎖: {floor_id}"

            # 3. 隨機選擇怪物群組
            monster_group_ids = floor_config.get('monster_group_ids', [])
            if not monster_group_ids:
                return None, f"樓層沒有配置怪物群組: {floor_id}"

            selected_monster_group_id = random.choice(monster_group_ids)
            monster_group_config = self.config_loader.get_monster_group_config(selected_monster_group_id)
            if not monster_group_config:
                return None, f"怪物群組配置不存在: {selected_monster_group_id}"

            # 4. 創建玩家隊伍
            player_team, player_error = self._create_player_team(user_id, user_progress)
            if player_error:
                return None, player_error

            # 5. 創建怪物隊伍
            monster_team, monster_error = self._create_monster_team(monster_group_config)
            if monster_error:
                return None, monster_error

            # 6. 創建戰鬥實例
            battle = Battle(
                player_team=player_team,
                monster_team=monster_team,
                config_loader=self.config_loader,
                effect_applier=self.effect_applier,
                target_selector=self.target_selector,
                passive_trigger_handler=self.passive_trigger_handler,
                formula_evaluator=self.formula_evaluator
            )

            logger.info(f"戰鬥準備完成: battle_id={battle.battle_id}")
            return battle, None

        except Exception as e:
            logger.error(f"準備PVE戰鬥失敗: {e}")
            return None, f"準備戰鬥失敗: {str(e)}"

    def _create_player_team(self, user_id: int, user_progress) -> Tuple[List[Combatant], Optional[str]]:
        """
        創建玩家隊伍

        Args:
            user_id: 用戶ID
            user_progress: 用戶進度數據

        Returns:
            (戰鬥者列表, 錯誤信息)
        """
        try:
            player_team = []

            # 獲取當前隊伍編成
            current_team_formation = user_progress.current_team_formation or []
            if not current_team_formation:
                return [], "玩家沒有設置隊伍編成"

            for collection_id in current_team_formation:
                # 獲取玩家卡牌數據
                player_card_data = self.player_collection_repo.get_player_card_by_collection_id(collection_id)
                if not player_card_data:
                    logger.warning(f"玩家卡牌數據不存在: collection_id={collection_id}")
                    continue

                # 獲取卡牌配置
                card_config = self.config_loader.get_card_config(player_card_data.card_id)
                if not card_config:
                    logger.warning(f"卡牌配置不存在: card_id={player_card_data.card_id}")
                    continue

                # 創建戰鬥者
                combatant = self._create_player_combatant(player_card_data, card_config)
                if combatant:
                    player_team.append(combatant)

            if not player_team:
                return [], "無法創建有效的玩家隊伍"

            logger.info(f"玩家隊伍創建完成: {len(player_team)}個戰鬥者")
            return player_team, None

        except Exception as e:
            logger.error(f"創建玩家隊伍失敗: {e}")
            return [], f"創建玩家隊伍失敗: {str(e)}"

    def _create_player_combatant(self, player_card_data, card_config) -> Optional[Combatant]:
        """
        創建玩家戰鬥者

        Args:
            player_card_data: 玩家卡牌數據
            card_config: 卡牌配置

        Returns:
            戰鬥者實例或None
        """
        try:
            # 獲取基礎屬性
            base_stats_config = card_config.get('base_stats', {})
            base_stats = CombatantStats(
                hp=base_stats_config.get('hp', 100),
                max_mp=base_stats_config.get('max_mp', 50),
                mp_regen_per_turn=base_stats_config.get('mp_regen_per_turn', 5),
                patk=base_stats_config.get('patk', 20),
                pdef=base_stats_config.get('pdef', 10),
                matk=base_stats_config.get('matk', 15),
                mdef=base_stats_config.get('mdef', 8),
                spd=base_stats_config.get('spd', 10),
                crit_rate=base_stats_config.get('crit_rate', 0.1),
                crit_dmg_multiplier=base_stats_config.get('crit_dmg_multiplier', 1.5),
                accuracy=base_stats_config.get('accuracy', 0.9),
                evasion=base_stats_config.get('evasion', 0.1)
            )

            # 創建戰鬥者實例
            combatant = Combatant(
                combatant_id=f"player_{player_card_data.id}",
                name=card_config.get('name', f"Card_{player_card_data.card_id}"),
                stats=base_stats,
                is_player=True,
                card_id=player_card_data.card_id,
                star_level=player_card_data.star_level,
                rpg_level=player_card_data.rpg_level,
                definition_id=player_card_data.card_id
            )

            # 添加技能
            self._setup_player_combatant_skills(combatant, player_card_data, card_config)

            # 計算最終屬性
            calculated_stats = self.attribute_calculator.calculate_attributes(
                definition_id=player_card_data.card_id,
                definition_type="CARD",
                rpg_level=player_card_data.rpg_level,
                star_level=player_card_data.star_level
            )

            # 更新戰鬥者屬性
            if calculated_stats:
                combatant.base_stats = CombatantStats(**calculated_stats)
                combatant.current_hp = calculated_stats.get('hp', combatant.current_hp)
                combatant.current_mp = 0  # 戰鬥開始時MP為0

            return combatant

        except Exception as e:
            logger.error(f"創建玩家戰鬥者失敗: {e}")
            return None

    def _setup_player_combatant_skills(self, combatant: Combatant, player_card_data, card_config):
        """
        設置玩家戰鬥者技能

        Args:
            combatant: 戰鬥者實例
            player_card_data: 玩家卡牌數據
            card_config: 卡牌配置
        """
        try:
            # 1. 設置普攻
            primary_attack_id = card_config.get('primary_attack_skill_id')
            if primary_attack_id:
                primary_attack = SkillInstance(
                    skill_id=primary_attack_id,
                    skill_type=SkillType.PRIMARY_ATTACK,
                    current_level=1,
                    current_cooldown=0
                )
                combatant.add_skill(primary_attack)

            # 2. 設置已裝備的主動技能
            equipped_active_skills = player_card_data.equipped_active_skill_ids or []
            for skill_id in equipped_active_skills:
                if skill_id:  # 跳過空槽位
                    active_skill = SkillInstance(
                        skill_id=skill_id,
                        skill_type=SkillType.ACTIVE,
                        current_level=1,  # TODO: 從全局技能數據獲取等級
                        current_cooldown=0
                    )
                    combatant.add_skill(active_skill)

            # 3. 設置天賦被動技能
            innate_passive_id = card_config.get('innate_passive_skill_id')
            if innate_passive_id:
                innate_passive = SkillInstance(
                    skill_id=innate_passive_id,
                    skill_type=SkillType.INNATE_PASSIVE,
                    current_level=player_card_data.star_level,  # 天賦等級通常與星級相關
                    current_cooldown=0
                )
                combatant.add_skill(innate_passive)

            # 4. 設置已裝備的通用被動技能
            equipped_passives = player_card_data.equipped_common_passives or {}
            for slot_key, passive_data in equipped_passives.items():
                if passive_data and isinstance(passive_data, dict):
                    skill_id = passive_data.get('skill_id')
                    skill_level = passive_data.get('level', 1)
                    if skill_id:
                        passive_skill = SkillInstance(
                            skill_id=skill_id,
                            skill_type=SkillType.PASSIVE,
                            current_level=skill_level,
                            current_cooldown=0
                        )
                        combatant.add_skill(passive_skill)

        except Exception as e:
            logger.error(f"設置玩家戰鬥者技能失敗: {e}")

    def _create_monster_team(self, monster_group_config) -> Tuple[List[Combatant], Optional[str]]:
        """
        創建怪物隊伍

        Args:
            monster_group_config: 怪物群組配置

        Returns:
            (戰鬥者列表, 錯誤信息)
        """
        try:
            monster_team = []

            monsters_in_group = monster_group_config.get('monsters_in_group', [])
            if not monsters_in_group:
                return [], "怪物群組沒有配置怪物"

            for i, monster_in_group in enumerate(monsters_in_group):
                monster_id = monster_in_group.get('monster_id')
                if not monster_id:
                    continue

                # 獲取怪物配置
                monster_config = self.config_loader.get_monster_config(monster_id)
                if not monster_config:
                    logger.warning(f"怪物配置不存在: monster_id={monster_id}")
                    continue

                # 創建怪物戰鬥者
                combatant = self._create_monster_combatant(monster_config, i)
                if combatant:
                    monster_team.append(combatant)

            if not monster_team:
                return [], "無法創建有效的怪物隊伍"

            logger.info(f"怪物隊伍創建完成: {len(monster_team)}個戰鬥者")
            return monster_team, None

        except Exception as e:
            logger.error(f"創建怪物隊伍失敗: {e}")
            return [], f"創建怪物隊伍失敗: {str(e)}"

    def _create_monster_combatant(self, monster_config, index: int) -> Optional[Combatant]:
        """
        創建怪物戰鬥者

        Args:
            monster_config: 怪物配置
            index: 怪物在群組中的索引

        Returns:
            戰鬥者實例或None
        """
        try:
            # 獲取基礎屬性
            base_stats_config = monster_config.get('base_stats', {})
            base_stats = CombatantStats(
                hp=base_stats_config.get('hp', 80),
                max_mp=base_stats_config.get('max_mp', 30),
                mp_regen_per_turn=base_stats_config.get('mp_regen_per_turn', 3),
                patk=base_stats_config.get('patk', 18),
                pdef=base_stats_config.get('pdef', 8),
                matk=base_stats_config.get('matk', 12),
                mdef=base_stats_config.get('mdef', 6),
                spd=base_stats_config.get('spd', 10),
                crit_rate=base_stats_config.get('crit_rate', 0.05),
                crit_dmg_multiplier=base_stats_config.get('crit_dmg_multiplier', 1.3),
                accuracy=base_stats_config.get('accuracy', 0.85),
                evasion=base_stats_config.get('evasion', 0.05)
            )

            # 創建戰鬥者實例
            monster_id = monster_config.get('monster_id', f"monster_{index}")
            combatant = Combatant(
                combatant_id=f"monster_{monster_id}_{index}",
                name=monster_config.get('name', f"Monster_{monster_id}"),
                stats=base_stats,
                is_player=False,
                definition_id=monster_id
            )

            # 添加技能
            self._setup_monster_combatant_skills(combatant, monster_config)

            # 計算最終屬性（怪物通常不需要RPG等級和星級計算）
            calculated_stats = self.attribute_calculator.calculate_attributes(
                definition_id=monster_id,
                definition_type="MONSTER",
                rpg_level=1,
                star_level=0
            )

            # 更新戰鬥者屬性
            if calculated_stats:
                combatant.base_stats = CombatantStats(**calculated_stats)
                combatant.current_hp = calculated_stats.get('hp', combatant.current_hp)
                combatant.current_mp = 0  # 戰鬥開始時MP為0

            return combatant

        except Exception as e:
            logger.error(f"創建怪物戰鬥者失敗: {e}")
            return None

    def _setup_monster_combatant_skills(self, combatant: Combatant, monster_config):
        """
        設置怪物戰鬥者技能

        Args:
            combatant: 戰鬥者實例
            monster_config: 怪物配置
        """
        try:
            skill_order_preference = []

            # 1. 設置普攻
            primary_attack_id = monster_config.get('primary_attack_skill_id')
            if primary_attack_id:
                primary_attack = SkillInstance(
                    skill_id=primary_attack_id,
                    skill_type=SkillType.PRIMARY_ATTACK,
                    current_level=1,
                    current_cooldown=0
                )
                combatant.add_skill(primary_attack)
                skill_order_preference.append(primary_attack_id)

            # 2. 設置主動技能
            active_skill_ids = monster_config.get('active_skill_ids', [])
            for skill_id in active_skill_ids:
                if skill_id:
                    active_skill = SkillInstance(
                        skill_id=skill_id,
                        skill_type=SkillType.ACTIVE,
                        current_level=1,
                        current_cooldown=0
                    )
                    combatant.add_skill(active_skill)
                    skill_order_preference.append(skill_id)

            # 3. 設置被動技能
            passive_skill_ids = monster_config.get('passive_skill_ids', [])
            for skill_id in passive_skill_ids:
                if skill_id:
                    passive_skill = SkillInstance(
                        skill_id=skill_id,
                        skill_type=SkillType.PASSIVE,
                        current_level=1,
                        current_cooldown=0
                    )
                    combatant.add_skill(passive_skill)

            # 設置AI技能使用順序
            combatant.skill_order_preference = skill_order_preference

        except Exception as e:
            logger.error(f"設置怪物戰鬥者技能失敗: {e}")

    def start_battle(self, battle: Battle) -> Battle:
        """
        啟動戰鬥

        Args:
            battle: 戰鬥實例

        Returns:
            戰鬥實例
        """
        try:
            logger.info(f"啟動戰鬥: {battle.battle_id}")
            battle.start()
            return battle
        except Exception as e:
            logger.error(f"啟動戰鬥失敗: {e}")
            raise BattleCoordinatorServiceError(f"啟動戰鬥失敗: {str(e)}") from e

    def process_player_action(
        self,
        battle: Battle,
        acting_player_combatant_id: str,
        skill_id: str,
        target_combatant_ids: List[str]
    ) -> Tuple[Battle, Optional[str]]:
        """
        處理玩家行動

        Args:
            battle: 戰鬥實例
            acting_player_combatant_id: 行動玩家戰鬥者ID
            skill_id: 技能ID
            target_combatant_ids: 目標戰鬥者ID列表

        Returns:
            (更新後的戰鬥實例, 錯誤信息)
        """
        try:
            # 驗證當前行動者
            current_actor = battle.get_acting_combatant()
            if not current_actor:
                return battle, "沒有當前行動者"

            if current_actor.instance_id != acting_player_combatant_id:
                return battle, f"不是當前行動者: {acting_player_combatant_id}"

            if not current_actor.is_player_side:
                return battle, "當前行動者不是玩家"

            # 處理行動
            battle.process_action(current_actor, skill_id, target_combatant_ids)

            logger.info(f"玩家行動處理完成: {acting_player_combatant_id} 使用 {skill_id}")
            return battle, None

        except Exception as e:
            logger.error(f"處理玩家行動失敗: {e}")
            return battle, f"處理玩家行動失敗: {str(e)}"

    def advance_battle_turn(self, battle: Battle) -> Battle:
        """
        推進戰鬥回合（處理AI行動直到輪到玩家或戰鬥結束）

        Args:
            battle: 戰鬥實例

        Returns:
            更新後的戰鬥實例
        """
        try:
            logger.debug(f"推進戰鬥回合: {battle.battle_id}")

            # 推進到下一個行動者
            current_actor = battle.next_turn_or_combatant()

            # 循環處理AI行動直到輪到玩家或戰鬥結束
            while (current_actor and
                   not current_actor.is_player_side and
                   battle.battle_status == BattleStatus.IN_PROGRESS):

                logger.debug(f"處理AI行動: {current_actor.name}")

                # 獲取AI行動
                ai_skill_id, ai_target_ids = current_actor.get_ai_action(battle, self.config_loader)

                if ai_skill_id:
                    # 執行AI行動
                    battle.process_action(current_actor, ai_skill_id, ai_target_ids)
                    logger.info(f"AI行動完成: {current_actor.name} 使用 {ai_skill_id}")
                else:
                    logger.warning(f"AI無法選擇行動: {current_actor.name}")

                # 檢查戰鬥是否結束
                if battle.battle_status != BattleStatus.IN_PROGRESS:
                    break

                # 推進到下一個行動者
                current_actor = battle.next_turn_or_combatant()

            return battle

        except Exception as e:
            logger.error(f"推進戰鬥回合失敗: {e}")
            raise BattleCoordinatorServiceError(f"推進戰鬥回合失敗: {str(e)}") from e

    def handle_pve_battle_completion(self, user_id: int, battle: Battle, floor_id: str) -> Dict[str, Any]:
        """
        處理PVE戰鬥完成

        Args:
            user_id: 用戶ID
            battle: 戰鬥實例
            floor_id: 樓層ID

        Returns:
            包含戰鬥結果、獎勵等信息的字典
        """
        try:
            logger.info(f"處理PVE戰鬥完成: user_id={user_id}, battle_id={battle.battle_id}, result={battle.battle_status.value}")

            # 獲取樓層配置
            floor_config = self.config_loader.get_floor_config(floor_id)
            if not floor_config:
                return {
                    "success": False,
                    "error": f"樓層配置不存在: {floor_id}",
                    "battle_result": battle.battle_status.value
                }

            result = {
                "success": True,
                "battle_result": battle.battle_status.value,
                "battle_id": battle.battle_id,
                "floor_id": floor_id,
                "is_first_clear": False,
                "rewards": [],
                "progress_updated": False
            }

            # 只有玩家勝利時才處理進度和獎勵
            if battle.battle_status == BattleStatus.PLAYER_WIN:
                # 獲取用戶進度
                user_progress = self.user_progress_repo.get_user_progress(user_id)
                if not user_progress:
                    result["error"] = f"用戶進度不存在: {user_id}"
                    return result

                # 增加勝利次數
                user_progress.current_floor_wins += 1

                # 確定獎勵類型
                rewards_key_to_grant = None
                is_first_clear = False

                if user_progress.max_floor_cleared < int(floor_id):
                    # 首次通關
                    rewards_key_to_grant = floor_config.get('first_clear_rewards_key')
                    user_progress.max_floor_cleared = int(floor_id)
                    is_first_clear = True
                    result["is_first_clear"] = True
                else:
                    # 重複通關
                    rewards_key_to_grant = floor_config.get('repeatable_rewards_per_win_key')

                # 檢查是否達到晉級條件
                wins_required = floor_config.get('wins_required_to_advance', 1)
                if user_progress.current_floor_wins >= wins_required:
                    # 解鎖下一層
                    next_floor = int(floor_id) + 1
                    user_progress.current_floor_unlocked = max(user_progress.current_floor_unlocked, next_floor)
                    user_progress.current_floor_wins = 0  # 重置勝利次數
                    result["floor_advanced"] = True
                    result["next_floor_unlocked"] = next_floor

                # 更新用戶進度
                self.user_progress_repo.update_user_progress(user_progress)
                result["progress_updated"] = True

                # 處理獎勵
                if rewards_key_to_grant:
                    rewards = self._get_rewards_from_config(rewards_key_to_grant)
                    result["rewards"] = rewards
                    # TODO: 實際發放獎勵到玩家背包

                logger.info(f"PVE戰鬥勝利處理完成: user_id={user_id}, 首次通關={is_first_clear}")

            return result

        except Exception as e:
            logger.error(f"處理PVE戰鬥完成失敗: {e}")
            return {
                "success": False,
                "error": f"處理戰鬥完成失敗: {str(e)}",
                "battle_result": battle.battle_status.value if battle else "UNKNOWN"
            }

    def _get_rewards_from_config(self, rewards_key: str) -> List[Dict[str, Any]]:
        """
        從配置獲取獎勵列表

        Args:
            rewards_key: 獎勵配置鍵

        Returns:
            獎勵列表
        """
        try:
            reward_packages = self.config_loader.reward_packages or {}
            reward_package = reward_packages.get(rewards_key, {})

            rewards = reward_package.get('rewards', [])
            return rewards if isinstance(rewards, list) else []

        except Exception as e:
            logger.error(f"獲取獎勵配置失敗: {e}")
            return []
